import request from '@/utils/request'

// 获取供应商列表
export function getProviders() {
  return request({
    url: '/api/provider/list',
    method: 'get'
  })
}

// 根据供应商ID获取商品列表
export function getGoodsByProvider(providerId) {
  return request({
    url: '/api/goods/list',
    method: 'get',
    params: { providerId }
  })
}

// 分页查询进货单
export function getInportList(params) {
  return request({
    url: '/api/inport/list',
    method: 'get',
    params
  })
}

// 获取进货单详情
export function getInportDetail(id) {
  return request({
    url: `/api/inport/${id}`,
    method: 'get'
  })
}

// 添加进货单
export function addInport(data) {
  return request({
    url: '/api/inport',
    method: 'post',
    data
  })
}

// 修改进货单
export function updateInport(id, data) {
  return request({
    url: `/api/inport/${id}`,
    method: 'put',
    data
  })
}

// 删除进货单
export function deleteInport(id) {
  return request({
    url: `/api/inport/${id}`,
    method: 'delete'
  })
}