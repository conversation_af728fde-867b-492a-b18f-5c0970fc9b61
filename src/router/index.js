import { createRouter, createWebHashHistory } from 'vue-router'

const routes = [
  {
    path: '/',
    redirect: '/home'
  },
  {
    path: '/home',
    name: 'Home',
    component: () => import('@/views/Home.vue'),
    meta: { title: '首页' }
  },
  {
    path: '/inport',
    name: 'InportLayout',
    component: () => import('@/views/layout/InportLayout.vue'),
    meta: { title: '进货管理' },
    children: [
      {
        path: '',
        redirect: '/inport/list'
      },
      {
        path: 'list',
        name: 'InportList',
        component: () => import('@/views/inport/InportList.vue'),
        meta: { title: '进货单查询' }
      },
      {
        path: 'add',
        name: 'InportAdd',
        component: () => import('@/views/inport/InportAdd.vue'),
        meta: { title: '添加进货单' }
      },
      {
        path: 'edit/:id',
        name: 'InportEdit',
        component: () => import('@/views/inport/InportEdit.vue'),
        meta: { title: '编辑进货单' }
      }
    ]
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: () => import('@/views/NotFound.vue')
  }
]

const router = createRouter({
  history: createWebHashHistory(),
  routes
})

// 路由守卫
router.beforeEach((to, from, next) => {
  // 设置页面标题
  if (to.meta.title) {
    document.title = `${to.meta.title} - 商品进货管理系统`
  }
  next()
})

export default router
