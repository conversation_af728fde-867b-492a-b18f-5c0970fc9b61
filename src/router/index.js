import { createRouter, createWebHashHistory } from 'vue-router'

const routes = [
  {
    path: '/inport/list',
    component: () => import('@/views/inport/InportList.vue')
  },
  {
    path: '/inport/add',
    component: () => import('@/views/inport/InportAdd.vue')
  },
  {
    path: '/inport/edit/:id',
    component: () => import('@/views/inport/InportEdit.vue')
  }
]

const router = createRouter({
  history: createWebHashHistory(),
  routes
})

export default router