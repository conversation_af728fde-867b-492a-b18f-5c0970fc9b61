<template>
  <div class="home-container">
    <el-container>
      <el-header class="header">
        <div class="header-content">
          <h1 class="title">商品进货管理系统</h1>
          <div class="user-info">
            <el-avatar :size="40" icon="User" />
            <span class="username">管理员</span>
          </div>
        </div>
      </el-header>
      
      <el-main class="main-content">
        <div class="welcome-section">
          <h2>欢迎使用商品进货管理系统</h2>
          <p class="description">
            本系统基于Vue3 + Element Plus开发，提供完整的商品进货管理功能
          </p>
        </div>

        <div class="feature-cards">
          <el-row :gutter="20">
            <el-col :span="8">
              <el-card class="feature-card" shadow="hover" @click="goToInportList">
                <div class="card-content">
                  <el-icon class="card-icon" :size="48" color="#409EFF">
                    <List />
                  </el-icon>
                  <h3>进货单查询</h3>
                  <p>查看和管理所有进货单记录，支持条件筛选和分页查询</p>
                </div>
              </el-card>
            </el-col>
            
            <el-col :span="8">
              <el-card class="feature-card" shadow="hover" @click="goToInportAdd">
                <div class="card-content">
                  <el-icon class="card-icon" :size="48" color="#67C23A">
                    <Plus />
                  </el-icon>
                  <h3>添加进货单</h3>
                  <p>录入新的进货信息，选择供应商和商品，记录进货详情</p>
                </div>
              </el-card>
            </el-col>
            
            <el-col :span="8">
              <el-card class="feature-card" shadow="hover">
                <div class="card-content">
                  <el-icon class="card-icon" :size="48" color="#E6A23C">
                    <DataAnalysis />
                  </el-icon>
                  <h3>数据统计</h3>
                  <p>查看进货数据统计分析，了解业务运营情况</p>
                </div>
              </el-card>
            </el-col>
          </el-row>
        </div>

        <div class="quick-stats">
          <h3>快速统计</h3>
          <el-row :gutter="20">
            <el-col :span="6">
              <el-statistic title="今日进货单" :value="todayCount" />
            </el-col>
            <el-col :span="6">
              <el-statistic title="本月进货单" :value="monthCount" />
            </el-col>
            <el-col :span="6">
              <el-statistic title="供应商数量" :value="providerCount" />
            </el-col>
            <el-col :span="6">
              <el-statistic title="商品种类" :value="goodsCount" />
            </el-col>
          </el-row>
        </div>
      </el-main>
    </el-container>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { User, List, Plus, DataAnalysis } from '@element-plus/icons-vue'

const router = useRouter()

// 统计数据
const todayCount = ref(0)
const monthCount = ref(0)
const providerCount = ref(0)
const goodsCount = ref(0)

// 跳转到进货单列表
const goToInportList = () => {
  router.push('/inport/list')
}

// 跳转到添加进货单
const goToInportAdd = () => {
  router.push('/inport/add')
}

// 加载统计数据
const loadStats = async () => {
  // 这里可以调用API获取真实的统计数据
  // 暂时使用模拟数据
  todayCount.value = 12
  monthCount.value = 156
  providerCount.value = 8
  goodsCount.value = 45
}

onMounted(() => {
  loadStats()
})
</script>

<style scoped>
.home-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.header {
  background: #fff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  padding: 0;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 100%;
  padding: 0 20px;
}

.title {
  margin: 0;
  color: #409EFF;
  font-size: 24px;
  font-weight: 600;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 10px;
}

.username {
  color: #606266;
  font-size: 14px;
}

.main-content {
  padding: 30px;
}

.welcome-section {
  text-align: center;
  margin-bottom: 40px;
}

.welcome-section h2 {
  color: #303133;
  margin-bottom: 10px;
  font-size: 28px;
  font-weight: 500;
}

.description {
  color: #606266;
  font-size: 16px;
  margin: 0;
}

.feature-cards {
  margin-bottom: 40px;
}

.feature-card {
  cursor: pointer;
  transition: transform 0.3s ease;
  height: 200px;
}

.feature-card:hover {
  transform: translateY(-5px);
}

.card-content {
  text-align: center;
  padding: 20px;
}

.card-icon {
  margin-bottom: 15px;
}

.card-content h3 {
  color: #303133;
  margin: 15px 0 10px;
  font-size: 18px;
  font-weight: 500;
}

.card-content p {
  color: #909399;
  font-size: 14px;
  line-height: 1.5;
  margin: 0;
}

.quick-stats {
  background: #fff;
  padding: 30px;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.quick-stats h3 {
  color: #303133;
  margin-bottom: 20px;
  font-size: 20px;
  font-weight: 500;
}
</style>
