<template>
  <div class="inport-add-container">
    <h3>添加进货单</h3>
    <el-form :model="form" :rules="rules" ref="formRef" label-width="100px" style="max-width: 600px;">
      <el-form-item label="供应商名称" prop="providerId">
        <el-select v-model="form.providerId" placeholder="选择供应商" @change="onProviderChange" style="width: 100%;">
          <el-option
            v-for="item in providerList"
            :key="item.id"
            :label="item.providername"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="商品名称" prop="goodsId">
        <el-select v-model="form.goodsId" placeholder="选择商品" style="width: 100%;" :disabled="!form.providerId">
          <el-option
            v-for="item in goodsList"
            :key="item.id"
            :label="item.goodsname"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="进货价格" prop="inportPrice">
        <el-input-number v-model="form.inportPrice" :min="0" :precision="2" style="width: 100%;" />
      </el-form-item>
      <el-form-item label="进货数量" prop="inportNum">
        <el-input-number v-model="form.inportNum" :min="1" style="width: 100%;" />
      </el-form-item>
      <el-form-item label="总金额">
        <el-input v-model="totalMoney" disabled />
      </el-form-item>
      <el-form-item label="进货时间" prop="inportTime">
        <el-date-picker
          v-model="form.inportTime"
          type="datetime"
          value-format="YYYY-MM-DD HH:mm:ss"
          placeholder="选择日期时间"
          style="width: 100%;"
        />
      </el-form-item>
    </el-form>
    <div class="form-footer">
      <el-button type="primary" @click="submitForm">提交</el-button>
      <el-button @click="resetForm">重置</el-button>
      <el-button @click="cancel">取消</el-button>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getProviders, getGoodsByProvider, addInport } from '@/api/inport'

// 表单引用
const formRef = ref()

// 表单数据
const form = ref({
  providerId: '',
  goodsId: '',
  inportPrice: 0,
  inportNum: 1,
  inportTime: ''
})

// 计算总金额
const totalMoney = computed(() => {
  return (form.value.inportPrice * form.value.inportNum).toFixed(2)
})

// 验证规则
const rules = {
  providerId: [
    { required: true, message: '请选择供应商', trigger: 'change' }
  ],
  goodsId: [
    { required: true, message: '请选择商品', trigger: 'change' }
  ],
  inportPrice: [
    { required: true, message: '请输入进货价格', trigger: 'blur' },
    { type: 'number', min: 0, message: '价格必须大于等于0', trigger: 'blur' }
  ],
  inportNum: [
    { required: true, message: '请输入进货数量', trigger: 'blur' },
    { type: 'number', min: 1, message: '数量必须大于0', trigger: 'blur' }
  ],
  inportTime: [
    { required: true, message: '请选择进货时间', trigger: 'change' }
  ]
}

// 供应商和商品列表
const providerList = ref([])
const goodsList = ref([])

// 获取供应商列表
const loadProviders = async () => {
  try {
    const res = await getProviders()
    providerList.value = res.data || []
  } catch (error) {
    console.error('获取供应商列表失败:', error)
    ElMessage.error('获取供应商列表失败')
  }
}

// 根据供应商ID获取商品列表
const loadGoods = async (providerId) => {
  if (!providerId) {
    goodsList.value = []
    return
  }
  try {
    const res = await getGoodsByProvider(providerId)
    goodsList.value = res.data || []
  } catch (error) {
    console.error('获取商品列表失败:', error)
    ElMessage.error('获取商品列表失败')
  }
}

// 处理供应商变化
const onProviderChange = (providerId) => {
  form.value.goodsId = '' // 清空商品选择
  loadGoods(providerId)
}

// 提交表单
const submitForm = async () => {
  if (!formRef.value) return
  
  formRef.value.validate(async (valid) => {
    if (valid) {
      try {
        const data = {
          payType: '银联', // 支付方式（驼峰命名更符合前端习惯）
          inportTime: form.value.inportTime, // 保持一致性
          operatePerson: 'admin', // 操作员
          number: form.value.inportNum,
          remark: '',
          inportPrice: form.value.inportPrice,
          providerId: form.value.providerId,
          goodsId: form.value.goodsId
        }
        await addInport(data)
        ElMessage.success('添加成功')
        resetForm()
      } catch (error) {
        console.error('添加进货单失败:', error)
        ElMessage.error('添加失败')
      }
    } else {
      ElMessage.error('请填写完整信息')
    }
  })
}

// 重置表单
const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields()
  }
  form.value.inportPrice = 0
  form.value.inportNum = 1
}

// 取消
const cancel = () => {
  ElMessageBox.confirm('确定要离开吗？未保存的数据将会丢失。', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    // 返回上一页
    window.history.back()
  }).catch(() => {
    // 取消
  })
}

// 页面加载时初始化
loadProviders()
</script>

<style scoped>
.inport-add-container {
  padding: 20px;
}
.form-footer {
  margin-top: 30px;
  text-align: right;
}
h3 {
  margin-bottom: 20px;
  color: #1f2329;
}
</style>