<template>
  <div class="inport-edit-container">
    <h3>修改进货单</h3>
    <el-form :model="form" :rules="rules" ref="formRef" label-width="100px" style="max-width: 600px;">
      <el-form-item label="供应商名称" prop="providerId">
        <el-select v-model="form.providerId" placeholder="选择供应商" @change="onProviderChange" style="width: 100%;" :disabled="loading">
          <el-option
            v-for="item in providerList"
            :key="item.id"
            :label="item.providername"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="商品名称" prop="goodsId">
        <el-select v-model="form.goodsId" placeholder="选择商品" style="width: 100%;" :disabled="!form.providerId || loading">
          <el-option
            v-for="item in goodsList"
            :key="item.id"
            :label="item.goodsname"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="进货价格" prop="inportPrice">
        <el-input-number v-model="form.inportPrice" :min="0" :precision="2" style="width: 100%;" :disabled="loading" />
      </el-form-item>
      <el-form-item label="进货数量" prop="inportNum">
        <el-input-number v-model="form.inportNum" :min="1" style="width: 100%;" :disabled="loading" />
      </el-form-item>
      <el-form-item label="总金额">
        <el-input v-model="totalMoney" disabled />
      </el-form-item>
      <el-form-item label="进货时间" prop="inportTime">
        <el-date-picker
          v-model="form.inportTime"
          type="datetime"
          value-format="YYYY-MM-DD HH:mm:ss"
          placeholder="选择日期时间"
          style="width: 100%;"
          :disabled="loading"
        />
      </el-form-item>
    </el-form>
    <div class="form-footer">
      <el-button type="primary" @click="submitForm" :loading="loading">提交</el-button>
      <el-button @click="cancel">取消</el-button>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getProviders, getGoodsByProvider, getInportDetail, updateInport } from '@/api/inport'
import { useRoute, useRouter } from 'vue-router'

// 路由相关
const route = useRoute()
const router = useRouter()

// 表单引用
const formRef = ref()

// 加载状态
const loading = ref(false)

// 表单数据
const form = ref({
  providerId: '',
  goodsId: '',
  inportPrice: 0,
  inportNum: 1,
  inportTime: ''
})

// 计算总金额
const totalMoney = computed(() => {
  return (form.value.inportPrice * form.value.inportNum).toFixed(2)
})

// 验证规则
const rules = {
  providerId: [
    { required: true, message: '请选择供应商', trigger: 'change' }
  ],
  goodsId: [
    { required: true, message: '请选择商品', trigger: 'change' }
  ],
  inportPrice: [
    { required: true, message: '请输入进货价格', trigger: 'blur' },
    { type: 'number', min: 0, message: '价格必须大于等于0', trigger: 'blur' }
  ],
  inportNum: [
    { required: true, message: '请输入进货数量', trigger: 'blur' },
    { type: 'number', min: 1, message: '数量必须大于0', trigger: 'blur' }
  ],
  inportTime: [
    { required: true, message: '请选择进货时间', trigger: 'change' }
  ]
}

// 供应商和商品列表
const providerList = ref([])
const goodsList = ref([])

// 获取供应商列表
const loadProviders = async () => {
  try {
    const res = await getProviders()
    providerList.value = res.data || []
  } catch (error) {
    console.error('获取供应商列表失败:', error)
    ElMessage.error('获取供应商列表失败')
  }
}

// 根据供应商ID获取商品列表
const loadGoods = async (providerId) => {
  if (!providerId) {
    goodsList.value = []
    return
  }
  try {
    const res = await getGoodsByProvider(providerId)
    goodsList.value = res.data || []
  } catch (error) {
    console.error('获取商品列表失败:', error)
    ElMessage.error('获取商品列表失败')
  }
}

// 处理供应商变化
const onProviderChange = (providerId) => {
  form.value.goodsId = '' // 清空商品选择
  loadGoods(providerId)
}

// 获取进货单详情
const loadInportDetail = async () => {
  const id = route.params.id
  if (!id) {
    ElMessage.error('缺少进货单ID')
    router.back()
    return
  }
  
  loading.value = true
  try {
    const res = await getInportDetail(id)
    const data = res.data
    if (data) {
      form.value = {
        providerId: data.providerid,
        goodsId: data.goodsid,
        inportPrice: data.inportprice,
        inportNum: data.number,
        inportTime: data.inporttime
      }
      // 加载供应商列表并选中对应商品
      await loadProviders()
      await loadGoods(data.providerid)
    } else {
      ElMessage.error('未找到进货单信息')
      router.back()
    }
  } catch (error) {
    console.error('获取进货单详情失败:', error)
    ElMessage.error('获取详情失败')
    router.back()
  } finally {
    loading.value = false
  }
}

// 提交表单
const submitForm = async () => {
  if (!formRef.value) return
  
  formRef.value.validate(async (valid) => {
    if (valid) {
      loading.value = true
      try {
        const id = route.params.id
        const data = {
          paytype: '银联',           // 支付方式
          inporttime: form.value.inportTime,  // 进货时间
          operateperson: 'admin',   // 操作人
          number: form.value.inportNum,       // 数量
          remark: '',               // 备注
          inportprice: form.value.inportPrice, // 进货单价
          providerid: form.value.providerId,   // 供应商ID
          goodsid: form.value.goodsId         // 商品ID
        }
        await updateInport(id, data)
        ElMessage.success('修改成功')
        // 返回上一页
        router.back()
      } catch (error) {
        console.error('修改进货单失败:', error)
        ElMessage.error('修改失败')
      } finally {
        loading.value = false
      }
    } else {
      ElMessage.error('请填写完整信息')
    }
  })
}

// 取消
const cancel = () => {
  ElMessageBox.confirm('确定要离开吗？未保存的数据将会丢失。', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    // 返回上一页
    router.back()
  }).catch(() => {
    // 取消
  })
}

// 页面加载时初始化
onMounted(() => {
  loadInportDetail()
})
</script>

<style scoped>
.inport-edit-container {
  padding: 20px;
}
.form-footer {
  margin-top: 30px;
  text-align: right;
}
h3 {
  margin-bottom: 20px;
  color: #1f2329;
}
</style>