<template>
  <div class="inport-list-container">
    <div class="search-form">
      <el-form :inline="true" :model="searchForm" class="demo-form-inline">
        <el-form-item label="供应商名称">
          <el-select v-model="searchForm.providerId" placeholder="选择供应商" @change="onProviderChange" clearable>
            <el-option
              v-for="item in providerList"
              :key="item.id"
              :label="item.providername"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="商品名称">
          <el-select v-model="searchForm.goodsId" placeholder="选择商品" clearable>
            <el-option
              v-for="item in goodsList"
              :key="item.id"
              :label="item.goodsname"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSearch">查询</el-button>
          <el-button @click="onReset">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <div class="table-operation">
      <el-button type="primary" size="small" @click="handleAdd">添加进货</el-button>
    </div>

    <el-table :data="tableData" style="width: 100%" border stripe>
      <el-table-column prop="id" label="进货单号" width="180" />
      <el-table-column prop="providername" label="供应商名称" width="180" />
      <el-table-column prop="goodsname" label="商品名称" width="180" />
      <el-table-column prop="inportprice" label="进货价格" width="120" />
      <el-table-column prop="number" label="进货数量" width="120" />
      <el-table-column :formatter="formatMoney" label="总金额" width="120" />
      <el-table-column prop="inporttime" label="进货时间" width="180" />
      <el-table-column label="操作" width="150" fixed="right">
        <template #default="scope">
          <el-button link type="primary" size="small" @click="handleEdit(scope.row)">编辑</el-button>
          <el-button link type="danger" size="small" @click="handleDelete(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <div class="pagination-container">
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="currentPage"
        :page-sizes="[10, 20, 30, 50]"
        :page-size="pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getProviders, getGoodsByProvider, getInportList, deleteInport } from '@/api/inport'

// 搜索表单
const searchForm = ref({
  providerId: '',
  goodsId: ''
})

// 数据列表
const tableData = ref([])
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(10)

// 供应商和商品列表
const providerList = ref([])
const goodsList = ref([])

// 格式化金额显示
const formatMoney = (row, column, value) => {
  return `¥${(row.inportprice * row.number).toFixed(2)}`
}

// 获取供应商列表
const loadProviders = async () => {
  try {
    const res = await getProviders()
    providerList.value = res.data || []
  } catch (error) {
    console.error('获取供应商列表失败:', error)
  }
}

// 根据供应商ID获取商品列表
const loadGoods = async (providerId) => {
  if (!providerId) {
    goodsList.value = []
    return
  }
  try {
    const res = await getGoodsByProvider(providerId)
    goodsList.value = res.data || []
  } catch (error) {
    console.error('获取商品列表失败:', error)
  }
}

// 处理供应商变化
const onProviderChange = (providerId) => {
  searchForm.value.goodsId = '' // 清空商品选择
  loadGoods(providerId)
}

// 查询进货单列表
const loadInportList = async () => {
  try {
    const params = {
      pageNum: currentPage.value,
      pageSize: pageSize.value,
      providerId: searchForm.value.providerId,
      goodsId: searchForm.value.goodsId
    }
    const res = await getInportList(params)
    tableData.value = res.data?.list || []
    total.value = res.data?.total || 0
  } catch (error) {
    console.error('查询进货单列表失败:', error)
  }
}

// 搜索
const onSearch = () => {
  currentPage.value = 1 // 重置到第一页
  loadInportList()
}

// 重置
const onReset = () => {
  searchForm.value = {
    providerId: '',
    goodsId: ''
  }
  goodsList.value = []
  currentPage.value = 1
  loadInportList()
}

// 分页大小变化
const handleSizeChange = (val) => {
  pageSize.value = val
  loadInportList()
}

// 当前页变化
const handleCurrentChange = (val) => {
  currentPage.value = val
  loadInportList()
}

// 添加进货
const handleAdd = () => {
  window.location.hash = '#/inport/add'
}

// 编辑
const handleEdit = (row) => {
  window.location.hash = `#/inport/edit/${row.id}`
}

// 删除
const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm(`确定要删除进货单 ${row.id} 吗？`, '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    await deleteInport(row.id)
    ElMessage.success('删除成功')
    loadInportList() // 刷新列表
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除进货单失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

// 页面加载时初始化数据
onMounted(() => {
  loadProviders()
  loadInportList()
})
</script>

<style scoped>
.inport-list-container {
  padding: 20px;
}
.search-form {
  margin-bottom: 20px;
}
.table-operation {
  margin-bottom: 20px;
}
.pagination-container {
  margin-top: 20px;
  text-align: right;
}
</style>