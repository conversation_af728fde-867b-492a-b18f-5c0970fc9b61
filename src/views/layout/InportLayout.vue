<template>
  <div class="inport-layout">
    <el-container>
      <el-header class="header">
        <div class="header-content">
          <div class="left-section">
            <el-button 
              type="text" 
              @click="goHome" 
              class="home-btn"
            >
              <el-icon><House /></el-icon>
              返回首页
            </el-button>
            <el-divider direction="vertical" />
            <h2 class="page-title">进货管理</h2>
          </div>
          
          <div class="nav-menu">
            <el-menu
              :default-active="activeMenu"
              mode="horizontal"
              @select="handleMenuSelect"
              class="header-menu"
            >
              <el-menu-item index="/inport/list">
                <el-icon><List /></el-icon>
                进货单查询
              </el-menu-item>
              <el-menu-item index="/inport/add">
                <el-icon><Plus /></el-icon>
                添加进货单
              </el-menu-item>
            </el-menu>
          </div>
        </div>
      </el-header>
      
      <el-main class="main-content">
        <div class="content-wrapper">
          <router-view />
        </div>
      </el-main>
    </el-container>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { House, List, Plus } from '@element-plus/icons-vue'

const router = useRouter()
const route = useRoute()

// 当前激活的菜单项
const activeMenu = computed(() => {
  const path = route.path
  if (path.includes('/inport/add')) {
    return '/inport/add'
  } else if (path.includes('/inport/edit')) {
    return '/inport/list' // 编辑页面时高亮列表菜单
  } else {
    return '/inport/list'
  }
})

// 处理菜单选择
const handleMenuSelect = (index) => {
  router.push(index)
}

// 返回首页
const goHome = () => {
  router.push('/home')
}
</script>

<style scoped>
.inport-layout {
  min-height: 100vh;
  background: #f5f7fa;
}

.header {
  background: #fff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  padding: 0;
  height: 60px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 100%;
  padding: 0 20px;
}

.left-section {
  display: flex;
  align-items: center;
  gap: 15px;
}

.home-btn {
  color: #409EFF;
  font-size: 14px;
  padding: 8px 12px;
}

.home-btn:hover {
  background-color: #ecf5ff;
}

.page-title {
  margin: 0;
  color: #303133;
  font-size: 18px;
  font-weight: 500;
}

.nav-menu {
  flex: 1;
  display: flex;
  justify-content: center;
}

.header-menu {
  border-bottom: none;
  background: transparent;
}

.header-menu .el-menu-item {
  border-bottom: 2px solid transparent;
  color: #606266;
  font-weight: 500;
}

.header-menu .el-menu-item:hover {
  color: #409EFF;
  background-color: #ecf5ff;
}

.header-menu .el-menu-item.is-active {
  color: #409EFF;
  border-bottom-color: #409EFF;
  background-color: #ecf5ff;
}

.main-content {
  padding: 20px;
}

.content-wrapper {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  min-height: calc(100vh - 120px);
  padding: 20px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    height: auto;
    padding: 10px 20px;
  }
  
  .left-section {
    width: 100%;
    justify-content: space-between;
    margin-bottom: 10px;
  }
  
  .nav-menu {
    width: 100%;
  }
  
  .header-menu {
    justify-content: center;
  }
  
  .main-content {
    padding: 10px;
  }
}
</style>
