/*
 Navicat Premium Data Transfer

 Source Server         : localhost
 Source Server Type    : MySQL
 Source Server Version : 80028
 Source Host           : 127.0.0.1:3306
 Source Schema         : warehouse

 Target Server Type    : MySQL
 Target Server Version : 80028
 File Encoding         : 65001

 Date: 10/08/2023 17:15:02
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for bus_inport
-- ----------------------------
DROP TABLE IF EXISTS `bus_inport`;
CREATE TABLE `bus_inport` (
  `id` int NOT NULL AUTO_INCREMENT,
  `paytype` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `inporttime` datetime DEFAULT NULL,
  `operateperson` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `number` int DEFAULT NULL,
  `remark` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `inportprice` double DEFAULT NULL,
  `providerid` int DEFAULT NULL,
  `goodsid` int DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `bus_inport_ibfk_1` (`providerid`) USING BTREE,
  KEY `bus_inport_ibfk_2` (`goodsid`) USING BTREE,
  CONSTRAINT `bus_inport_ibfk_1` FOREIGN KEY (`providerid`) REFERENCES `bus_provider` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `bus_inport_ibfk_2` FOREIGN KEY (`goodsid`) REFERENCES `bus_goods` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB AUTO_INCREMENT=19 DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Records of bus_inport
-- ----------------------------
BEGIN;
INSERT INTO `bus_inport` (`id`, `paytype`, `inporttime`, `operateperson`, `number`, `remark`, `inportprice`, `providerid`, `goodsid`) VALUES (1, '微信', '2018-05-07 00:00:00', '张三', 100, '备注', 3.5, 1, 1);
INSERT INTO `bus_inport` (`id`, `paytype`, `inporttime`, `operateperson`, `number`, `remark`, `inportprice`, `providerid`, `goodsid`) VALUES (2, '支付宝', '2018-05-07 00:00:00', '张三', 1000, '无', 2.5, 3, 3);
INSERT INTO `bus_inport` (`id`, `paytype`, `inporttime`, `operateperson`, `number`, `remark`, `inportprice`, `providerid`, `goodsid`) VALUES (3, '银联', '2018-05-07 00:00:00', '张三', 100, '1231', 111, 3, 3);
INSERT INTO `bus_inport` (`id`, `paytype`, `inporttime`, `operateperson`, `number`, `remark`, `inportprice`, `providerid`, `goodsid`) VALUES (4, '银联', '2018-05-07 00:00:00', '张三', 1000, '无', 2, 3, 1);
INSERT INTO `bus_inport` (`id`, `paytype`, `inporttime`, `operateperson`, `number`, `remark`, `inportprice`, `providerid`, `goodsid`) VALUES (5, '银联', '2018-05-07 00:00:00', '张三', 100, '无', 1, 3, 1);
INSERT INTO `bus_inport` (`id`, `paytype`, `inporttime`, `operateperson`, `number`, `remark`, `inportprice`, `providerid`, `goodsid`) VALUES (6, '银联', '2018-05-07 00:00:00', '张三', 100, '1231', 2.5, 1, 2);
INSERT INTO `bus_inport` (`id`, `paytype`, `inporttime`, `operateperson`, `number`, `remark`, `inportprice`, `providerid`, `goodsid`) VALUES (8, '支付宝', '2018-05-07 00:00:00', '张三', 100, '', 1, 3, 1);
INSERT INTO `bus_inport` (`id`, `paytype`, `inporttime`, `operateperson`, `number`, `remark`, `inportprice`, `providerid`, `goodsid`) VALUES (10, '支付宝', '2018-08-07 17:17:57', '超级管理员', 100, 'sadfasfdsa', 1.5, 3, 1);
INSERT INTO `bus_inport` (`id`, `paytype`, `inporttime`, `operateperson`, `number`, `remark`, `inportprice`, `providerid`, `goodsid`) VALUES (11, '支付宝', '2018-09-17 16:12:25', '超级管理员', 21, '21', 21, 1, 3);
INSERT INTO `bus_inport` (`id`, `paytype`, `inporttime`, `operateperson`, `number`, `remark`, `inportprice`, `providerid`, `goodsid`) VALUES (12, '微信', '2018-12-25 16:48:24', '超级管理员', 100, '123213213', 12321321, 3, 1);
INSERT INTO `bus_inport` (`id`, `paytype`, `inporttime`, `operateperson`, `number`, `remark`, `inportprice`, `providerid`, `goodsid`) VALUES (18, '银联', '2023-08-10 08:47:49', '超级管理员', 999, 'opopooopb', 9.9, 1, 2);
COMMIT;

SET FOREIGN_KEY_CHECKS = 1;
